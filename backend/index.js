const express = require('express');
const dotenv = require('dotenv');
const cookieParser = require('cookie-parser')
const cors = require('cors')
import {mongoDB} from './db/db';

dotenv.config();


const app = express();

app.use((err,req,res,next)=>{
  console.log(err.stack);
  res.status(500).json({error: 'internal server error'});
});

const PORT = process.env.PORT || 5000
app.listen(PORT,()=>{
  mongoDB();
  console.log(`server is running on the port ${PORT}`);
  console.log(`http://localhost:${PORT}`);
})

