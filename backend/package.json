{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "nodemon index.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^17.2.2", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.18.2", "nodemon": "^3.1.10"}}